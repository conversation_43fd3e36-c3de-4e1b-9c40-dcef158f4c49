-- =====================================================================================
-- ROOT CAUSE ANALYSIS: VW_FSE_INVENTORY Row Count Discrepancy
-- =====================================================================================
-- ISSUE: Original view has 122,948 rows, Optimized view has 122,868 rows (80 row difference)
-- ROOT CAUSE: Inconsistent Plant filtering in original query
-- =====================================================================================

-- PROBLEM IDENTIFIED:
-- In the original query (ORGINAL_VW_FSE_INVENTORY.SQL):
-- - Line 106: OUT movements have Plant filter: "AND vw_MaterialDocuments.Plant = '6399'"
-- - Line 167: IN movements MISSING Plant filter (only has date filter)
-- 
-- This means the original query includes IN movements from ALL plants,
-- but only OUT movements from Plant 6399, creating inconsistent data.

-- VALIDATION QUERY 1: Check Plant distribution in base material documents
-- =====================================================================================
SELECT 
    'BASE_MATERIAL_DOCS_ALL_PLANTS' as analysis,
    Plant,
    InOut,
    COUNT(*) as record_count
FROM (
    SELECT DISTINCT
        vw_MaterialDocuments.Plant,
        vw_MaterialDocuments.Dc_Ind as InOut,
        vw_MaterialDocuments.Doc_Date,
        vw_MaterialDocuments.Stor_Loc,
        vw_MaterialDocSerialNumbers.Serial_Num,
        vw_MaterialDocuments.Material_Num
    FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
    LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
        ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
            AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
    WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
        AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
        AND vw_MaterialDocuments.Dc_Ind IN ('S', 'H')
)
GROUP BY Plant, InOut
ORDER BY Plant, InOut;

-- VALIDATION QUERY 2: Simulate original query's inconsistent filtering
-- =====================================================================================
WITH original_logic_simulation AS (
    -- OUT movements with Plant filter (correct)
    SELECT 'OUT_WITH_PLANT_FILTER' as movement_type, COUNT(*) as count
    FROM (
        SELECT DISTINCT
            vw_MaterialDocuments.Doc_Date,
            vw_MaterialDocuments.Stor_Loc,
            vw_MaterialDocSerialNumbers.Serial_Num,
            vw_MaterialDocuments.Material_Num
        FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
        LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
            ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
                AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
        WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
            AND vw_MaterialDocuments.Plant = '6399'  -- Plant filter applied
            AND vw_MaterialDocuments.Dc_Ind = 'H'
            AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
    )
    
    UNION ALL
    
    -- IN movements WITHOUT Plant filter (original bug)
    SELECT 'IN_WITHOUT_PLANT_FILTER' as movement_type, COUNT(*) as count
    FROM (
        SELECT DISTINCT
            vw_MaterialDocuments.Doc_Date,
            vw_MaterialDocuments.Stor_Loc,
            vw_MaterialDocSerialNumbers.Serial_Num,
            vw_MaterialDocuments.Material_Num
        FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
        LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
            ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
                AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
        WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
            -- NO Plant filter here (this is the bug in original)
            AND vw_MaterialDocuments.Dc_Ind = 'S'
            AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
    )
    
    UNION ALL
    
    -- IN movements WITH Plant filter (optimized/correct)
    SELECT 'IN_WITH_PLANT_FILTER' as movement_type, COUNT(*) as count
    FROM (
        SELECT DISTINCT
            vw_MaterialDocuments.Doc_Date,
            vw_MaterialDocuments.Stor_Loc,
            vw_MaterialDocSerialNumbers.Serial_Num,
            vw_MaterialDocuments.Material_Num
        FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
        LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
            ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
                AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
        WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
            AND vw_MaterialDocuments.Plant = '6399'  -- Plant filter applied (correct)
            AND vw_MaterialDocuments.Dc_Ind = 'S'
            AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
    )
)
SELECT * FROM original_logic_simulation;

-- VALIDATION QUERY 3: Calculate the exact difference
-- =====================================================================================
SELECT 
    'DIFFERENCE_CALCULATION' as analysis,
    (SELECT COUNT(*) FROM (
        SELECT DISTINCT
            vw_MaterialDocuments.Doc_Date,
            vw_MaterialDocuments.Stor_Loc,
            vw_MaterialDocSerialNumbers.Serial_Num,
            vw_MaterialDocuments.Material_Num
        FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
        LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
            ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
                AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
        WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
            AND vw_MaterialDocuments.Dc_Ind = 'S'
            AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
            AND vw_MaterialDocuments.Plant != '6399'  -- Non-China plants
    )) as non_china_in_movements,
    
    'These are the extra records in original due to missing Plant filter' as explanation;

-- VALIDATION QUERY 4: Show sample records that would be excluded
-- =====================================================================================
SELECT 
    'SAMPLE_EXCLUDED_RECORDS' as analysis,
    Plant,
    Dc_Ind as InOut,
    COUNT(*) as count,
    MIN(Doc_Date) as earliest_date,
    MAX(Doc_Date) as latest_date
FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
    ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
        AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
WHERE vw_MaterialDocuments.Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP()))
    AND vw_MaterialDocuments.Dc_Ind = 'S'
    AND vw_MaterialDocSerialNumbers.Serial_Num IS NOT NULL
    AND vw_MaterialDocuments.Plant != '6399'  -- These would be excluded in optimized version
GROUP BY Plant, Dc_Ind
ORDER BY Plant;

-- =====================================================================================
-- CONCLUSION AND RECOMMENDATION
-- =====================================================================================
/*
FINDINGS:
1. The original query has inconsistent Plant filtering
2. OUT movements are filtered to Plant='6399' (China only)
3. IN movements include ALL plants (missing Plant filter)
4. This creates logically inconsistent data

RECOMMENDATION:
The optimized query is CORRECT and fixes a bug in the original query.
The 80-row difference represents IN movements from non-China plants that 
should have been excluded for data consistency.

BUSINESS IMPACT:
- Original query: Mixed data (China OUT movements + Global IN movements)
- Optimized query: Consistent data (China-only movements)
- This aligns with the comment "only china data" in the original query

ACTION REQUIRED:
1. Confirm with business users that China-only data is the requirement
2. If confirmed, the optimized query is correct
3. Update documentation to note this as a bug fix, not a data loss
4. If global data is needed, remove Plant filter from both queries
*/

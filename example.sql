CREATE OR REPLACE VIEW EDWSBX.RMARTIN.VW_SERVICES_FS_SU_QUARTERLYSYSTEMUPTIME (
    SYS_SERIAL_NUM,
    AVAILABLE,
    DOWN_TIME,
    UP_TIME,
    UPTIME_PERCENT,
    QUARTER_END_DATE,
    PORT_FILTER,
    DW_LOAD_TS
)
COMMENT='This view calculates the quarterly system uptime on-the-fly. It is a standalone object replacing the deprecated USP_SERVICES_FS_SU_QUARTERLYSYSTEMUPTIME and its target table. Logic is preserved from the original USP.'
AS
/* **************************************************************************************
   Name:         EDW.SERVICES_SHARED.VW_SERVICES_FS_SU_QUARTERLYSYSTEMUPTIME
** 
** Desc:         This view is a direct replacement for the deprecated stored procedure. 
**               It incorporates new logic for QuarterEndDate based on the revised 
**               vw_InstallBaseSnapshot and adds the QUARTER_INDICATOR filter, while 
**               preserving all other original calculation logic.
**
** Author:       RANJITH KUMAR
** Date:         26/06/2025
****************************************************************************************
**       CHANGE HISTORY
****************************************************************************************
**  Date         Author                 Description                                Version
**  ----------   ---------------------- ------------------------------------------ ---------
**  26/06/2025    RANJITH KUMAR           Initial code based on CHGXXXXXXX.            V1
**                                      - Converted from USP/Table to a standalone View.
**                                      - Updated QuarterEndDate & added QUARTER_INDICATOR filter.
**                                      - Preserved original date and calculation logic.
**                                      - CORRECTED last_day() function to use 'QUARTER'.
****************************************************************************************/
SELECT
      SysSerialNum   AS SYS_SERIAL_NUM
    , Available      AS AVAILABLE
    , DownTime       AS DOWN_TIME
    , UpTime         AS UP_TIME
    , UptimePercent  AS UPTIME_PERCENT
    , QuarterEndDate AS QUARTER_END_DATE
    , PortFilter     AS PORT_FILTER
    , DWLoadTS       AS DW_LOAD_TS
FROM (
   
	SELECT
		sn."SystemSerialNumber" AS SysSerialNum,
        (TRUNC((365*24*60)/4)) AS Available,
		NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0) AS DownTime,
		(TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0) AS UpTime,
		CASE
			WHEN cast(((TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2)) ),0.0))/(TRUNC((365*24*60)/4)) AS DECIMAL(10,6))  < 0
		    THEN 0
		    ELSE cast(((TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2)) ),0.0))/(TRUNC((365*24*60)/4)) AS DECIMAL(10,6))
		END AS UptimePercent,
		last_day(TO_DATE(REPLACE(sn."SnapshotQuarter", ' Q', '-') || '-01', 'YYYY-MM-DD'), 'QUARTER') AS QuarterEndDate,
		CASE WHEN "SystemModel" = 'da Vinci SP' THEN 'Single Port' ELSE 'Multi Port' END AS PortFilter,
        CURRENT_TIMESTAMP()::TIMESTAMP AS DWLoadTS
	FROM
		EDW.FINANCESHARED."vw_InstallBaseSnapshot" sn
	LEFT JOIN
		EDW.FIELDSERVICE.VW_CRMSERVICEORDER so
		ON so.system_serial_num = sn."SystemSerialNumber"
		AND DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), to_timestamp_ntz(Work_End_DateTime_LCL)) +1, ('01/01/1900'::DATE))) = last_day(TO_DATE(REPLACE(sn."SnapshotQuarter", ' Q', '-') || '-01', 'YYYY-MM-DD'), 'QUARTER')
	LEFT JOIN
		EDW.MASTER.VW_ACCOUNT inf ON inf.AccountID = sn."AccountID"
	WHERE
		last_day(TO_DATE(REPLACE(sn."SnapshotQuarter", ' Q', '-') || '-01', 'YYYY-MM-DD'), 'QUARTER') BETWEEN to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), CURRENT_TIMESTAMP()::TIMESTAMP) -13, ('01/01/1900'::DATE)))) AND to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), CURRENT_TIMESTAMP()::TIMESTAMP), ('01/01/1900'::DATE))))
        AND sn.QUARTER_INDICATOR = TRUE
		AND ACCOUNTNAME NOT LIKE '%intuitive%'
		AND ACCOUNTNAME NOT LIKE '%bldg%'
		AND ACCOUNTNAME NOT LIKE '%mobile unit%'
		AND ACCOUNTNAME NOT LIKE '%truck%'
	GROUP BY
		sn."SystemSerialNumber",
        sn."SnapshotQuarter",
		sn."SystemModel"

	UNION

	SELECT
		inf.Sys_Serial_Num AS SysSerialNum,
		(TRUNC((365*24*60)/4)) AS Available,
		NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0) AS DownTime,
		(TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0) AS UpTime,
		CASE
			WHEN cast(((TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0))/(TRUNC((365*24*60)/4)) AS DECIMAL(10,6)) < 0
			THEN 0
			ELSE cast(((TRUNC((365*24*60)/4)) - NVL(SUM(cast(NVL(DOWNTIME_IN_MINUTES,0) AS DECIMAL(10,2))),0.0))/(TRUNC((365*24*60)/4)) AS DECIMAL(10,6))
		END AS UptimePercent,
		to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), inf.install_date) + 1, ('01/01/1900'::DATE)))) AS QuarterEndDate,
		'Single Port' AS PortFilter,
        CURRENT_TIMESTAMP()::TIMESTAMP AS DWLoadTS
	FROM
		EDW.FIELDSERVICE.vw_CrmSystemInformation inf
	LEFT JOIN
		EDW.FIELDSERVICE.vw_CrmServiceOrder so
		ON so.System_Serial_Num = inf.Sys_Serial_Num
		AND DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), to_timestamp_ntz(Work_End_DateTime_LCL)) + 1, ('01/01/1900'::DATE))) = to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), inf.install_date) + 1, ('01/01/1900'::DATE))))
	WHERE
		to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), inf.install_date) + 1, ('01/01/1900'::DATE)))) BETWEEN to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), CURRENT_TIMESTAMP()::TIMESTAMP) - 13, ('01/01/1900'::DATE)))) AND to_timestamp_ntz(DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, ('01/01/1900'::DATE), CURRENT_TIMESTAMP()::TIMESTAMP), ('01/01/1900'::DATE))))
		AND Sys_Serial_Num LIKE 'en%'
		AND End_Customer_Name NOT LIKE '%intuitive%'
		AND End_Customer_Name NOT LIKE '%bldg%'
		AND End_Customer_Name NOT LIKE '%mobile unit%'
		AND End_Customer_Name NOT LIKE '%truck%'
		AND Is_Active = 1
	GROUP BY
		inf.Sys_Serial_Num,
		inf.install_date
) a;
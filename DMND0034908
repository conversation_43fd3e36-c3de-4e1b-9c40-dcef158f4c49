List of Objects Changed:
PROMOTE EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY TO EDW.SERVICES_SHARED.VW_FSE_INVENTORY

Reason for Change:
PROMOTION OF LAB OBJECT

Technical Details (Objects for Change):
OPTIMIZE/TUNE QUERY IN SNOWFLAKE
PUBLISH ANALYST WORK

Benefits for the End User:
Service analytics tracking of FSE global part inventory reporting

List of Connected Apps:
Tableau

List of Downstream Objects:
N/A

Is a full load required?
No

buisness case :
Service Analytics cleaning up old objects and setting up measures that will be applicable with SF360 changes upcoming in January

solution
optimiize and tune to CTE style query, allow analysts time to nest into workbooks, then later deprecate older inventory objects from Services_Shared

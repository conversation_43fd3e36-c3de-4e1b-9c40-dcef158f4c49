create or replace view ED<PERSON>LA<PERSON>.SERVICES_LAB.VW_FSE_INVENTORY(
	RECEIVEDDATE,
	STORLOC,
	SERI<PERSON>NUM,
	GOODBAD,
	MATE<PERSON><PERSON>NU<PERSON>,
	DAYSINBIN,
	R<PERSON>URNEDDA<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	PLANT,
	CRM_EMP_ID,
	FULL_NAME,
	FIRST_NAME,
	LAST_NAME,
	USER_ID,
	PERSONNEL_NUM,
	B<PERSON><PERSON>ESS_UNIT,
	STATE,
	COUNTRY,
	REG<PERSON>,
	DISTRIBUTOR,
	FE_START_DATE,
	TOP_REGION,
	STOR_LOC,
	STOR_LOC_DESC,
	DIVISION,
	DEPARTMENT,
	T<PERSON><PERSON>,
	JOB_FUNCTION,
	IS_FSE,
	FEDEX_ACCOUNT,
	IS_PRIMARY_REGION,
	IN_CRM,
	IS_SERVICEOPS,
	IS_DISTRIBUTOR,
	IS_DISTRIBUTOR_ACTIVE,
	DISTRIBUTOR_COUNTRY_CODE,
	DISTRIBUTOR_COUNTRY
) as
/******************************************************************************
Name:EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
** Desc: VW_FSE_INVENTORY
** Author: Marcel Friedmann
** Date: 7/9/2025
*******************************************************************************

**********************************CHANGE HISTORY*******************************
**	Date 			Author				Description
**-----------------------------------------------------------------------------
**	7/9/2025		Marcel Friedmann	Initial code
********************************************************************************/

-- Base CTE for common material document data to eliminate duplication
WITH base_material_docs AS (
	SELECT DISTINCT
		vw_MaterialDocuments.Doc_Date as DocDate,
		vw_MaterialDocuments.Stor_Loc as StorLoc,
		vw_MaterialDocuments.Plant,
		vw_MaterialDocuments.Material_Num as MaterialNum,
		vw_MaterialDocuments.Dc_Ind as InOut,
		vw_MaterialDocSerialNumbers.Equipment_Num as EquipmentNum,
		vw_MaterialDocSerialNumbers.Serial_Num as SerialNum,
		vw_MaterialDocuments.Material_Doc_Num,
		MAX(vw_MaterialDocuments.Move_Type) as MoveType
	FROM EDW.INVENTORY.VW_MATERIALDOCUMENTS vw_MaterialDocuments
	LEFT JOIN EDW.INVENTORY.VW_MATERIALDOCSERIALNUMBERS vw_MaterialDocSerialNumbers
		ON (vw_MaterialDocuments.Material_Doc_Num = vw_MaterialDocSerialNumbers.Material_Doc_Num
			AND vw_MaterialDocuments.Material_Doc_Item = vw_MaterialDocSerialNumbers.Material_Doc_Item)
	WHERE Doc_Date > to_timestamp_ntz(DATEADD(year, -2, CURRENT_TIMESTAMP() :: TIMESTAMP))
		AND vw_MaterialDocuments.Plant = '6399' -- only china data
	GROUP BY
		vw_MaterialDocuments.Dc_Ind,
		vw_MaterialDocuments.Doc_Date,
		vw_MaterialDocuments.Stor_Loc,
		vw_MaterialDocuments.Plant,
		vw_MaterialDocSerialNumbers.Equipment_Num,
		vw_MaterialDocSerialNumbers.Serial_Num,
		vw_MaterialDocuments.Material_Doc_Num,
		vw_MaterialDocuments.Material_Num
),

-- Material movements IN (received items)
material_movements_in AS (
	SELECT
		DocDate as ReceivedDate,
		StorLoc,
		Plant,
		SerialNum,
		ROW_NUMBER() OVER (
			PARTITION BY SerialNum, StorLoc, MaterialNum
			ORDER BY DocDate DESC
		) as Ranking,
		CASE
			WHEN (MoveType = '927' OR MoveType = '949') THEN 'Bad'
			ELSE 'Good'
		END as GoodBad,
		MaterialNum,
		InOut
	FROM base_material_docs
	WHERE InOut = 'S' AND SerialNum IS NOT NULL
),

-- Material movements OUT (returned items)
material_movements_out AS (
	SELECT
		DocDate as ReturnedDate,
		StorLoc,
		Plant,
		SerialNum,
		ROW_NUMBER() OVER (
			PARTITION BY SerialNum, StorLoc, MaterialNum
			ORDER BY DocDate DESC
		) as Ranking,
		MaterialNum,
		InOut
	FROM base_material_docs
	WHERE InOut = 'H' AND SerialNum IS NOT NULL
),

-- Orders CTE combining IN and OUT movements
Orders AS (
	SELECT
		"In".ReceivedDate as ReceivedDate,
		"In".StorLoc as StorLoc,
		"In".Plant as Plant,
		"In".SerialNum as SerialNum,
		"In".GoodBad as GoodBad,
		"In".MaterialNum as MaterialNum,
		DATEDIFF(day, "In".ReceivedDate, Out.ReturnedDate) as DaysInBin,
		Out.ReturnedDate as ReturnedDate,
		CASE
			WHEN DATEDIFF(day, "In".ReceivedDate, Out.ReturnedDate) > 12.0 THEN '>12 days'
			WHEN DATEDIFF(day, "In".ReceivedDate, Out.ReturnedDate) > 10.0 THEN '>10-12 days'
			WHEN DATEDIFF(day, "In".ReceivedDate, Out.ReturnedDate) > 7.0 THEN '>7-10 days'
			ELSE '<=7 days'
		END as Timeframe
	FROM material_movements_out Out
	LEFT JOIN material_movements_in "In"
		ON (Out.StorLoc = "In".StorLoc
			AND Out.SerialNum = "In".SerialNum
			AND Out.MaterialNum = "In".MaterialNum
			AND Out.Ranking = "In".Ranking)
	WHERE "In".ReceivedDate < Out.ReturnedDate
),
-- Returns CTE for service order items (kept as-is from original)
Returns AS (
	SELECT
		SRVC_ORD_NUM,
		ORD_PROD_ID_DESC as MaterialDesc,
		PART_STATUS as GoodBad,
		DISMANT_PART_SERIAL_NUM as SerialNum,
		PART_NUM,
		OBJ_MATERIAL,
		Stor_Loc as StorLoc,
		Plant,
		EXEC_SRVC_EMP_ID,
		EXEC_SRVC_EMP_NAME,
		TO_DATE(LEFT(CREATED_AT,8), 'YYYYMMDD') as ReceivedDate,
		MIN(r.RETURN_DATE) AS ReturnedDate,
		DATEDIFF('day',TO_DATE(LEFT(CREATED_AT,8), 'YYYYMMDD'),MIN(r.RETURN_DATE)) AS DaysInBin,
		ORD_PROD_ID as MaterialNum,
		OBJ_SERIAL_NUM,
		CASE
			WHEN DATEDIFF(day, ReceivedDate, RETURN_DATE) > 12.0 THEN '>12 days'
			WHEN DATEDIFF(day, ReceivedDate, RETURN_DATE) > 10.0 THEN '>10-12 days'
			WHEN DATEDIFF(day, ReceivedDate, RETURN_DATE) > 7.0 THEN '>7-10 days'
			ELSE '<=7 days'
		END as Timeframe
	FROM EDW.FIELDSERVICE.VW_CRMSERVICEORDERITEM i
	LEFT JOIN (
		SELECT
			TO_DATE(S.DATUM, 'YYYYMMDD') AS RETURN_DATE,
			SERNR,
			LAGERORT AS STROAGE_LOCATION
		FROM ECC.ECC.SER03 S
		LEFT JOIN ECC.ECC.OBJK AS O ON O.OBKNR = S.OBKNR
		WHERE SHKZG='S' AND S.MJAHR > YEAR(DATEADD(year,-3,GETDATE()))
	) r ON i.dismant_part_serial_num = LTRIM(r.SERNR,'0')
		AND TO_DATE(LEFT(CREATED_AT,8), 'YYYYMMDD') < r.RETURN_DATE
	LEFT JOIN EDW.SERVICES_SHARE.VW_EMPLOYEEREGIONBIN T2 ON i.EXEC_SRVC_EMP_ID = T2.CRM_EMP_ID
	WHERE YEAR(TO_DATE(LEFT(CREATED_AT,8), 'YYYYMMDD')) > YEAR(DATEADD(year,-2,GETDATE()))
		AND DISMANT_PART_SERIAL_NUM IS NOT NULL
		AND r.RETURN_DATE IS NOT NULL
	GROUP BY ALL
)


-- Final SELECT combining Orders and Returns data with employee information
SELECT
	a.ReceivedDate,
	a.StorLoc,
	a.SerialNum,
	a.GoodBad,
	a.MaterialNum,
	a.DaysInBin,
	a.ReturnedDate,
	a.TimeFrame,
	a.Plant,
	b.crm_emp_id,
	b.full_name,
	b.first_name,
	b.last_name,
	b.user_id,
	b.personnel_num,
	b.business_unit,
	b.state,
	b.country,
	b.region,
	b.distributor,
	b.fe_start_date,
	b.top_region,
	b.stor_loc,
	b.stor_loc_desc,
	b.division,
	b.department,
	b.title,
	b.job_function,
	b.is_fse,
	b.fedex_account,
	b.is_primary_region,
	b.in_crm,
	b.is_serviceops,
	b.is_distributor,
	b.is_distributor_active,
	b.distributor_country_code,
	b.distributor_country
FROM (
	-- Union of Orders and Returns data
	SELECT
		ReceivedDate,
		StorLoc,
		SerialNum,
		GoodBad,
		MaterialNum,
		DaysInBin,
		ReturnedDate,
		TimeFrame,
		Plant
	FROM Orders

	UNION ALL

	SELECT
		ReceivedDate,
		StorLoc, -- get from empbin
		SerialNum,
		GoodBad,
		MaterialNum,
		DaysInBin,
		ReturnedDate,
		Timeframe,
		Plant  --'' AS plant --from empbin
	FROM Returns
) a
LEFT JOIN EDW.SERVICES_SHARE.VW_EMPLOYEEREGIONBIN b ON a.StorLoc = b.stor_loc
--WHERE a.Plant IN ('6399', '6499') AND country IN ('China','Hong Kong') -- only china and hongkong data
--WHERE clause prepared for China JV version
;
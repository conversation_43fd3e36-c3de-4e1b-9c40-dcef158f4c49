SELECT * FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY;

-- RESULT - 122.9K ROWS

-- SAMPLE 20 ROWS FROM RESULT

-- 2024-05-28	CTM1	10000609	Good	380663-25	8	2024-06-05	>7-10 days	6399																												
-- 2024-07-27	FD38	10000609	Good	380663-25	5	2024-08-01	<=7 days	6399	916577	ZHANG KEVIN	KEVIN	ZHANG	KZHANG2	10000321	Services	Shanghai	China	CN Capital		2020-10-12 00:00:00.000	CHINA	FD38	Kevin Zhang			SERVICE ENGINEER		N		Y						
-- 2024-08-07	CTM1	10001691	Good	380647-34	7	2024-08-14	<=7 days	6399																												
-- 2024-09-09	FC34	10001691	Good	380647-34	3	2024-09-12	<=7 days	6399	901567	BENJAMIN ASHLEY	ASHLEY	BENJAMIN	ASHLEYB	501502	Services		France			2010-09-21 00:00:00.000		FC34	DeactivatedFEBin			FLD SERVICE ENGR L	Customer Service and Field Support	Y		Y		Y				
-- 2024-09-09	FC34	10001691	Good	380647-34	3	2024-09-12	<=7 days	6399	916343	FENG RYAN	RYAN	FENG	RFENG	10000304	Services	Shanghai	China	CN South		2020-08-10 00:00:00.000	CHINA	FC34	Ryan Feng			STAFF ENGINEER, SERVICE	53008 - Service China JV	N		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	928835	LIU BINFENG	BINFENG	LIU	BLIU	10000774	Services	Shanghai	China	CN Eastern		2023-09-11 00:00:00.000	CHINA	FC35	Binfeng Liu			SR. ENGINEER, SERVICE		N		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	928835	LIU BINFENG	BINFENG	LIU	BLIU	10000774	Services	Shanghai	China	CN Eastern		2023-09-11 00:00:00.000	CHINA	FC35	Binfeng Liu			SR. ENGINEER, SERVICE		N		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	901601	BREMSAT MARCUS	MARCUS	BREMSAT	MARCUSB	501526	Services		Germany	EU DACH North		2011-01-17 00:00:00.000	EUROPE DIRECT	FC35	2875-M.Bremsat			FIELD SERVICE ENGINEER 4	Customer Service and Field Support Function	Y		Y						
-- 2025-02-26	FSC1	10001691	Good	380647-34	42	2025-04-09	>12 days	6399																												
-- 2025-02-11	FSC2	10001691	Good	380647-34	12	2025-02-23	>10-12 days	6399																												
-- 2024-08-14	FSC2	10001691	Good	380647-34	6	2024-08-20	<=7 days	6399																												
-- 2024-08-21	FSC3	10001691	Good	380647-34	19	2024-09-09	>12 days	6399																												
-- 2023-12-25	FSC1	100068	Good	320396-01	35	2024-01-29	>12 days	6399																												
-- 2023-08-16	FC17	10007309	Good	186213-01	8	2023-08-24	>7-10 days	6399	913591	LIU HAO	HAO	LIU	HLIU1	10000142	Services	Shanghai	China	daVinci Support China		2019-07-02 00:00:00.000		FC17	Hao Liu			MANAGER, DVSTAT	53008 - Service China JV	N		Y						
-- 2023-08-16	FC17	10007309	Good	186213-01	8	2023-08-24	>7-10 days	6399	901279	PICANCO LUCY	LUCY	PICANCO	LUCYP	501163	Services	California	United States			2010-03-18 00:00:00.000		FC17	DeactivatedFEBin			SERVICE CONTRACTS ADMIN	Sales	N		Y		Y				
-- 2024-08-05	FD51	10007309	Good	186213-01	9	2024-08-14	>7-10 days	6399	923682	MALLICK SANJOY	SANJOY	MALLICK	SMALLICK	527002	Services	New Jersey	United States			2022-05-16 00:00:00.000		FD51	Do Not Use			FIELD SERVICE ENGR 2	Customer Service and Field Sup	Y		Y		Y				
-- 2024-08-05	FD51	10007309	Good	186213-01	9	2024-08-14	>7-10 days	6399	925783	DONG YINGQIANG	YINGQIANG	DONG	YDONG1	10000602	Services	Shanghai	China	daVinci Support China		2022-09-19 00:00:00.000		FD51	Yingqiang Dong			DVSTAT ENGINEER		N		Y						
-- 2023-08-16	FC17	10007335	Good	186213-01	8	2023-08-24	>7-10 days	6399	913591	LIU HAO	HAO	LIU	HLIU1	10000142	Services	Shanghai	China	daVinci Support China		2019-07-02 00:00:00.000		FC17	Hao Liu			MANAGER, DVSTAT	53008 - Service China JV	N		Y						
-- 2023-08-16	FC17	10007335	Good	186213-01	8	2023-08-24	>7-10 days	6399	901279	PICANCO LUCY	LUCY	PICANCO	LUCYP	501163	Services	California	United States			2010-03-18 00:00:00.000		FC17	DeactivatedFEBin			SERVICE CONTRACTS ADMIN	Sales	N		Y		Y				
-- 2024-08-05	FD51	10007335	Good	186213-01	9	2024-08-14	>7-10 days	6399	923682	MALLICK SANJOY	SANJOY	MALLICK	SMALLICK	527002	Services	New Jersey	United States			2022-05-16 00:00:00.000		FD51	Do Not Use			FIELD SERVICE ENGR 2	Customer Service and Field Sup	Y		Y		Y				

SELECT * FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY;

-- 122.9K RESULTS - 20 SAMPLE RESULTS

-- 2024-05-28	CTM1	10000609	Good	380663-25	8	2024-06-05	>7-10 days	6399																												
-- 2024-07-27	FD38	10000609	Good	380663-25	5	2024-08-01	<=7 days	6399	916577	ZHANG KEVIN	KEVIN	ZHANG	KZHANG2	10000321	Services	Shanghai	China	CN Capital		2020-10-12 00:00:00.000	CHINA	FD38	Kevin Zhang			SERVICE ENGINEER		N		Y						
-- 2024-08-07	CTM1	10001691	Good	380647-34	7	2024-08-14	<=7 days	6399																												
-- 2024-09-09	FC34	10001691	Good	380647-34	3	2024-09-12	<=7 days	6399	901567	BENJAMIN ASHLEY	ASHLEY	BENJAMIN	ASHLEYB	501502	Services		France			2010-09-21 00:00:00.000		FC34	DeactivatedFEBin			FLD SERVICE ENGR L	Customer Service and Field Support	Y		Y		Y				
-- 2024-09-09	FC34	10001691	Good	380647-34	3	2024-09-12	<=7 days	6399	916343	FENG RYAN	RYAN	FENG	RFENG	10000304	Services	Shanghai	China	CN South		2020-08-10 00:00:00.000	CHINA	FC34	Ryan Feng			STAFF ENGINEER, SERVICE	53008 - Service China JV	N		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	901601	BREMSAT MARCUS	MARCUS	BREMSAT	MARCUSB	501526	Services		Germany	EU DACH North		2011-01-17 00:00:00.000	EUROPE DIRECT	FC35	2875-M.Bremsat			FIELD SERVICE ENGINEER 4	Customer Service and Field Support Function	Y		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	928835	LIU BINFENG	BINFENG	LIU	BLIU	10000774	Services	Shanghai	China	CN Eastern		2023-09-11 00:00:00.000	CHINA	FC35	Binfeng Liu			SR. ENGINEER, SERVICE		N		Y						
-- 2025-04-09	FC35	10001691	Good	380647-34	5	2025-04-14	<=7 days	6399	928835	LIU BINFENG	BINFENG	LIU	BLIU	10000774	Services	Shanghai	China	CN Eastern		2023-09-11 00:00:00.000	CHINA	FC35	Binfeng Liu			SR. ENGINEER, SERVICE		N		Y						
-- 2025-02-26	FSC1	10001691	Good	380647-34	42	2025-04-09	>12 days	6399																												
-- 2025-02-11	FSC2	10001691	Good	380647-34	12	2025-02-23	>10-12 days	6399																												
-- 2024-08-14	FSC2	10001691	Good	380647-34	6	2024-08-20	<=7 days	6399																												
-- 2024-08-21	FSC3	10001691	Good	380647-34	19	2024-09-09	>12 days	6399																												
-- 2023-12-25	FSC1	100068	Good	320396-01	35	2024-01-29	>12 days	6399																												
-- 2023-08-16	FC17	10007309	Good	186213-01	8	2023-08-24	>7-10 days	6399	901279	PICANCO LUCY	LUCY	PICANCO	LUCYP	501163	Services	California	United States			2010-03-18 00:00:00.000		FC17	DeactivatedFEBin			SERVICE CONTRACTS ADMIN	Sales	N		Y		Y				
-- 2023-08-16	FC17	10007309	Good	186213-01	8	2023-08-24	>7-10 days	6399	913591	LIU HAO	HAO	LIU	HLIU1	10000142	Services	Shanghai	China	daVinci Support China		2019-07-02 00:00:00.000		FC17	Hao Liu			MANAGER, DVSTAT	53008 - Service China JV	N		Y						
-- 2024-08-05	FD51	10007309	Good	186213-01	9	2024-08-14	>7-10 days	6399	923682	MALLICK SANJOY	SANJOY	MALLICK	SMALLICK	527002	Services	New Jersey	United States			2022-05-16 00:00:00.000		FD51	Do Not Use			FIELD SERVICE ENGR 2	Customer Service and Field Sup	Y		Y		Y				
-- 2024-08-05	FD51	10007309	Good	186213-01	9	2024-08-14	>7-10 days	6399	925783	DONG YINGQIANG	YINGQIANG	DONG	YDONG1	10000602	Services	Shanghai	China	daVinci Support China		2022-09-19 00:00:00.000		FD51	Yingqiang Dong			DVSTAT ENGINEER		N		Y						
-- 2023-08-16	FC17	10007335	Good	186213-01	8	2023-08-24	>7-10 days	6399	901279	PICANCO LUCY	LUCY	PICANCO	LUCYP	501163	Services	California	United States			2010-03-18 00:00:00.000		FC17	DeactivatedFEBin			SERVICE CONTRACTS ADMIN	Sales	N		Y		Y				
-- 2023-08-16	FC17	10007335	Good	186213-01	8	2023-08-24	>7-10 days	6399	913591	LIU HAO	HAO	LIU	HLIU1	10000142	Services	Shanghai	China	daVinci Support China		2019-07-02 00:00:00.000		FC17	Hao Liu			MANAGER, DVSTAT	53008 - Service China JV	N		Y						
-- 2024-08-05	FD51	10007335	Good	186213-01	9	2024-08-14	>7-10 days	6399	923682	MALLICK SANJOY	SANJOY	MALLICK	SMALLICK	527002	Services	New Jersey	United States			2022-05-16 00:00:00.000		FD51	Do Not Use			FIELD SERVICE ENGR 2	Customer Service and Field Sup	Y		Y		Y	


-- =====================================================================================
-- VW_FSE_INVENTORY DATA VALIDATION AND COMPARISON QUERIES
-- Purpose: Validate data integrity between original and optimized views
-- Date: 2025-07-29
-- =====================================================================================

-- SECTION 1: BASIC ROW COUNT COMPARISON
-- =====================================================================================

-- Current row counts (showing discrepancy)
SELECT 'ORIGINAL' as version, COUNT(*) as row_count FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
UNION ALL
SELECT 'OPTIMIZED' as version, COUNT(*) as row_count FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY;

-- Row count difference analysis
SELECT
    (SELECT COUNT(*) FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY) as original_count,
    (SELECT COUNT(*) FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY) as optimized_count,
    (SELECT COUNT(*) FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY) -
    (SELECT COUNT(*) FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY) as difference;

-- SECTION 2: DETAILED DATA COMPARISON
-- =====================================================================================

-- Compare key metrics between views
SELECT
    'ORIGINAL' as version,
    COUNT(*) as total_rows,
    COUNT(DISTINCT SerialNum) as unique_serials,
    COUNT(DISTINCT MaterialNum) as unique_materials,
    COUNT(DISTINCT StorLoc) as unique_locations,
    COUNT(CASE WHEN GoodBad = 'Good' THEN 1 END) as good_parts,
    COUNT(CASE WHEN GoodBad = 'Bad' THEN 1 END) as bad_parts,
    MIN(ReceivedDate) as earliest_received,
    MAX(ReturnedDate) as latest_returned,
    AVG(DaysInBin) as avg_days_in_bin
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY

UNION ALL

SELECT
    'OPTIMIZED' as version,
    COUNT(*) as total_rows,
    COUNT(DISTINCT SerialNum) as unique_serials,
    COUNT(DISTINCT MaterialNum) as unique_materials,
    COUNT(DISTINCT StorLoc) as unique_locations,
    COUNT(CASE WHEN GoodBad = 'Good' THEN 1 END) as good_parts,
    COUNT(CASE WHEN GoodBad = 'Bad' THEN 1 END) as bad_parts,
    MIN(ReceivedDate) as earliest_received,
    MAX(ReturnedDate) as latest_returned,
    AVG(DaysInBin) as avg_days_in_bin
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY;

-- SECTION 3: IDENTIFY MISSING RECORDS
-- =====================================================================================

-- Records in ORIGINAL but NOT in OPTIMIZED
SELECT 'MISSING_IN_OPTIMIZED' as status, COUNT(*) as count
FROM (
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate
    FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    EXCEPT
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate
    FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
);

-- Records in OPTIMIZED but NOT in ORIGINAL
SELECT 'EXTRA_IN_OPTIMIZED' as status, COUNT(*) as count
FROM (
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate
    FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
    EXCEPT
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate
    FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
);

-- SECTION 4: DETAILED MISSING RECORDS ANALYSIS
-- =====================================================================================

-- Show actual missing records (first 100)
SELECT 'MISSING_IN_OPTIMIZED' as issue_type, *
FROM (
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant, GoodBad, DaysInBin
    FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    EXCEPT
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant, GoodBad, DaysInBin
    FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
)
LIMIT 100;

-- Show extra records in optimized (first 100)
SELECT 'EXTRA_IN_OPTIMIZED' as issue_type, *
FROM (
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant, GoodBad, DaysInBin
    FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
    EXCEPT
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant, GoodBad, DaysInBin
    FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
)
LIMIT 100;

-- SECTION 5: PLANT FILTER ANALYSIS (ROOT CAUSE INVESTIGATION)
-- =====================================================================================

-- Check if missing records are related to Plant filtering
-- This investigates the suspected issue with Plant filter in original query
SELECT
    'PLANT_ANALYSIS' as analysis_type,
    Plant,
    COUNT(*) as record_count
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
GROUP BY Plant
ORDER BY Plant;

SELECT
    'PLANT_ANALYSIS_OPTIMIZED' as analysis_type,
    Plant,
    COUNT(*) as record_count
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
GROUP BY Plant
ORDER BY Plant;

-- Check for NULL plants in missing records
SELECT
    'NULL_PLANT_CHECK' as check_type,
    CASE WHEN Plant IS NULL THEN 'NULL_PLANT' ELSE 'HAS_PLANT' END as plant_status,
    COUNT(*) as count
FROM (
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant
    FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    EXCEPT
    SELECT SerialNum, StorLoc, MaterialNum, ReceivedDate, ReturnedDate, Plant
    FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
)
GROUP BY CASE WHEN Plant IS NULL THEN 'NULL_PLANT' ELSE 'HAS_PLANT' END;

-- SECTION 6: COMPONENT-WISE VALIDATION (Orders vs Returns)
-- =====================================================================================

-- Analyze Orders component separately
WITH original_orders AS (
    SELECT * FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    WHERE Plant = '6399' -- Orders should only have Plant 6399
),
optimized_orders AS (
    SELECT * FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
    WHERE Plant = '6399'
)
SELECT
    'ORDERS_COMPONENT' as component,
    'ORIGINAL' as version,
    COUNT(*) as row_count
FROM original_orders
UNION ALL
SELECT
    'ORDERS_COMPONENT' as component,
    'OPTIMIZED' as version,
    COUNT(*) as row_count
FROM optimized_orders;

-- Analyze Returns component separately
WITH original_returns AS (
    SELECT * FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    WHERE Plant != '6399' OR Plant IS NULL -- Returns may have different plants
),
optimized_returns AS (
    SELECT * FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
    WHERE Plant != '6399' OR Plant IS NULL
)
SELECT
    'RETURNS_COMPONENT' as component,
    'ORIGINAL' as version,
    COUNT(*) as row_count
FROM original_returns
UNION ALL
SELECT
    'RETURNS_COMPONENT' as component,
    'OPTIMIZED' as version,
    COUNT(*) as row_count
FROM optimized_returns;

-- SECTION 7: SAMPLE DATA VALIDATION
-- =====================================================================================

-- Compare sample records for specific serial numbers
SELECT 'SAMPLE_VALIDATION' as test_type, 'ORIGINAL' as version, *
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
WHERE SerialNum IN (
    SELECT SerialNum FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
    WHERE SerialNum IS NOT NULL
    LIMIT 5
)
ORDER BY SerialNum, ReceivedDate

UNION ALL

SELECT 'SAMPLE_VALIDATION' as test_type, 'OPTIMIZED' as version, *
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
WHERE SerialNum IN (
    SELECT SerialNum FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
    WHERE SerialNum IS NOT NULL
    LIMIT 5
)
ORDER BY SerialNum, ReceivedDate;

-- SECTION 8: TIMEFRAME AND CALCULATION VALIDATION
-- =====================================================================================

-- Validate DaysInBin calculations
SELECT
    'CALCULATION_CHECK' as test_type,
    SerialNum,
    ReceivedDate,
    ReturnedDate,
    DaysInBin,
    DATEDIFF(day, ReceivedDate, ReturnedDate) as calculated_days,
    CASE
        WHEN DaysInBin = DATEDIFF(day, ReceivedDate, ReturnedDate) THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as calculation_status,
    Timeframe,
    CASE
        WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 12.0 THEN '>12 days'
        WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 10.0 THEN '>10-12 days'
        WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 7.0 THEN '>7-10 days'
        ELSE '<=7 days'
    END as calculated_timeframe,
    CASE
        WHEN Timeframe = CASE
            WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 12.0 THEN '>12 days'
            WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 10.0 THEN '>10-12 days'
            WHEN DATEDIFF(day, ReceivedDate, ReturnedDate) > 7.0 THEN '>7-10 days'
            ELSE '<=7 days'
        END THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as timeframe_status
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
WHERE ReceivedDate IS NOT NULL AND ReturnedDate IS NOT NULL
LIMIT 20;

-- SECTION 9: DATA QUALITY CHECKS
-- =====================================================================================

-- Check for NULL values in critical fields
SELECT
    'NULL_CHECK_ORIGINAL' as test_type,
    SUM(CASE WHEN SerialNum IS NULL THEN 1 ELSE 0 END) as null_serialnum,
    SUM(CASE WHEN MaterialNum IS NULL THEN 1 ELSE 0 END) as null_materialnum,
    SUM(CASE WHEN StorLoc IS NULL THEN 1 ELSE 0 END) as null_storloc,
    SUM(CASE WHEN ReceivedDate IS NULL THEN 1 ELSE 0 END) as null_receiveddate,
    SUM(CASE WHEN ReturnedDate IS NULL THEN 1 ELSE 0 END) as null_returneddate,
    SUM(CASE WHEN GoodBad IS NULL THEN 1 ELSE 0 END) as null_goodbad,
    SUM(CASE WHEN Plant IS NULL THEN 1 ELSE 0 END) as null_plant
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY

UNION ALL

SELECT
    'NULL_CHECK_OPTIMIZED' as test_type,
    SUM(CASE WHEN SerialNum IS NULL THEN 1 ELSE 0 END) as null_serialnum,
    SUM(CASE WHEN MaterialNum IS NULL THEN 1 ELSE 0 END) as null_materialnum,
    SUM(CASE WHEN StorLoc IS NULL THEN 1 ELSE 0 END) as null_storloc,
    SUM(CASE WHEN ReceivedDate IS NULL THEN 1 ELSE 0 END) as null_receiveddate,
    SUM(CASE WHEN ReturnedDate IS NULL THEN 1 ELSE 0 END) as null_returneddate,
    SUM(CASE WHEN GoodBad IS NULL THEN 1 ELSE 0 END) as null_goodbad,
    SUM(CASE WHEN Plant IS NULL THEN 1 ELSE 0 END) as null_plant
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY;

-- Check GoodBad value distribution
SELECT
    'GOODBAD_DISTRIBUTION' as test_type,
    'ORIGINAL' as version,
    GoodBad,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
GROUP BY GoodBad

UNION ALL

SELECT
    'GOODBAD_DISTRIBUTION' as test_type,
    'OPTIMIZED' as version,
    GoodBad,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
GROUP BY GoodBad
ORDER BY test_type, version, GoodBad;

-- Check Timeframe distribution
SELECT
    'TIMEFRAME_DISTRIBUTION' as test_type,
    'ORIGINAL' as version,
    Timeframe,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY
GROUP BY Timeframe

UNION ALL

SELECT
    'TIMEFRAME_DISTRIBUTION' as test_type,
    'OPTIMIZED' as version,
    Timeframe,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY
GROUP BY Timeframe
ORDER BY test_type, version, Timeframe;

-- SECTION 10: PERFORMANCE COMPARISON
-- =====================================================================================

-- Query execution time comparison (run separately)
/*
-- Run this query to test ORIGINAL view performance:
SELECT 'ORIGINAL_PERFORMANCE_TEST' as test_type, CURRENT_TIMESTAMP() as start_time;
SELECT COUNT(*), AVG(DaysInBin), COUNT(DISTINCT SerialNum)
FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY;
SELECT 'ORIGINAL_PERFORMANCE_TEST' as test_type, CURRENT_TIMESTAMP() as end_time;

-- Run this query to test OPTIMIZED view performance:
SELECT 'OPTIMIZED_PERFORMANCE_TEST' as test_type, CURRENT_TIMESTAMP() as start_time;
SELECT COUNT(*), AVG(DaysInBin), COUNT(DISTINCT SerialNum)
FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY;
SELECT 'OPTIMIZED_PERFORMANCE_TEST' as test_type, CURRENT_TIMESTAMP() as end_time;
*/

-- SECTION 11: FINAL VALIDATION SUMMARY
-- =====================================================================================

-- Summary validation report
SELECT
    'VALIDATION_SUMMARY' as report_type,
    'Row Count Difference' as metric,
    CAST((SELECT COUNT(*) FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY) -
         (SELECT COUNT(*) FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY) AS VARCHAR) as value

UNION ALL

SELECT
    'VALIDATION_SUMMARY' as report_type,
    'Unique Serials Difference' as metric,
    CAST((SELECT COUNT(DISTINCT SerialNum) FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY) -
         (SELECT COUNT(DISTINCT SerialNum) FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY) AS VARCHAR) as value

UNION ALL

SELECT
    'VALIDATION_SUMMARY' as report_type,
    'Data Quality Status' as metric,
    CASE
        WHEN (SELECT COUNT(*) FROM EDWSBX.RMARTIN.VW_FSE_INVENTORY) =
             (SELECT COUNT(*) FROM EDWLABS.SERVICES_LAB.VW_FSE_INVENTORY)
        THEN 'PASS - Row counts match'
        ELSE 'FAIL - Row count mismatch detected'
    END as value;

-- =====================================================================================
-- RECOMMENDED ACTIONS BASED ON FINDINGS:
-- =====================================================================================
/*
1. If row count difference is due to Plant filtering:
   - Review the Plant filter logic in the original query
   - The original query may be missing Plant filter in the "In" movements section

2. If missing records are identified:
   - Analyze the specific records to understand the root cause
   - Check if the optimization inadvertently changed business logic

3. If calculations are incorrect:
   - Review the DATEDIFF and CASE statement logic
   - Ensure window functions are applied correctly

4. Performance validation:
   - Run the performance tests separately
   - Compare execution times and resource usage

5. Business validation:
   - Have business users validate sample data
   - Ensure all expected records are present
*/
